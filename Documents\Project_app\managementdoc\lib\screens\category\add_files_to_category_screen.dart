import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import 'dart:async';

import '../../core/constants/app_colors.dart';
import '../../providers/document_provider.dart';
import '../../models/category_model.dart';
import '../../models/document_model.dart';
import '../../widgets/common/app_bottom_navigation.dart';

class AddFilesToCategoryScreen extends StatefulWidget {
  final CategoryModel category;

  const AddFilesToCategoryScreen({super.key, required this.category});

  @override
  State<AddFilesToCategoryScreen> createState() =>
      _AddFilesToCategoryScreenState();
}

class _AddFilesToCategoryScreenState extends State<AddFilesToCategoryScreen> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _searchTimer;
  String _selectedFilter = 'all';
  Set<String> _selectedFiles = <String>{};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    final documentProvider = Provider.of<DocumentProvider>(
      context,
      listen: false,
    );
    await documentProvider.loadDocuments();
  }

  void _onSearchChanged() {
    if (_searchTimer?.isActive ?? false) _searchTimer!.cancel();
    _searchTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch();
    });
  }

  void _performSearch() {
    setState(() {
      // Trigger rebuild to apply filter
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      // Trigger rebuild to clear filter
    });
  }

  List<DocumentModel> _getAvailableDocuments(List<DocumentModel> allDocuments) {
    // Get documents that are NOT in this category
    final availableDocuments = allDocuments
        .where((doc) => doc.category != widget.category.id)
        .toList();

    // Apply search filter
    final searchQuery = _searchController.text.toLowerCase().trim();
    if (searchQuery.isEmpty) {
      return availableDocuments;
    }

    return availableDocuments.where((document) {
      final fileName = document.fileName.toLowerCase();
      final description = document.metadata.description.toLowerCase();
      final fileType = document.fileType.toLowerCase();

      return fileName.contains(searchQuery) ||
          description.contains(searchQuery) ||
          fileType.contains(searchQuery);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffoldWithNavigation(
      title: 'Add Files to ${widget.category.name}',
      currentNavIndex: 1, // Category tab
      showAppBar: true,
      actions: [
        if (_selectedFiles.isNotEmpty)
          TextButton(
            onPressed: _isLoading ? null : _addSelectedFiles,
            child: Text(
              'Add (${_selectedFiles.length})',
              style: GoogleFonts.poppins(
                color: _isLoading
                    ? AppColors.textSecondary
                    : AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
      body: Consumer<DocumentProvider>(
        builder: (context, documentProvider, child) {
          final availableDocuments = _getAvailableDocuments(
            documentProvider.documents,
          );

          if (documentProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            );
          }

          return RefreshIndicator(
            onRefresh: _loadData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // Search Section
                  _buildSearchSection(),

                  // Filter Section
                  _buildFilterSection(),

                  // Selected Files Info
                  if (_selectedFiles.isNotEmpty) _buildSelectedFilesInfo(),

                  // Files List
                  availableDocuments.isEmpty
                      ? _buildEmptyState()
                      : _buildFileTable(availableDocuments),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.searchBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search available files...',
                hintStyle: GoogleFonts.poppins(
                  color: AppColors.textSecondary,
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(
                          Icons.clear,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          _searchController.clear();
                          _clearSearch();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.surface,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.textPrimary,
              ),
              onSubmitted: (value) => _performSearch(),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _performSearch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.textWhite,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Search',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _clearSearch,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.surface,
                  foregroundColor: AppColors.textSecondary,
                  padding: const EdgeInsets.symmetric(
                    vertical: 12,
                    horizontal: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: AppColors.border),
                  ),
                ),
                child: Text(
                  'Clear',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Text(
            'Filter by type:',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('all', 'All Files'),
                  _buildFilterChip('pdf', 'PDF'),
                  _buildFilterChip('doc', 'Word'),
                  _buildFilterChip('image', 'Images'),
                  _buildFilterChip('excel', 'Excel'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: isSelected ? AppColors.textWhite : AppColors.textSecondary,
          ),
        ),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = value;
          });
        },
        backgroundColor: AppColors.surface,
        selectedColor: AppColors.primary,
        checkmarkColor: AppColors.textWhite,
        side: BorderSide(
          color: isSelected ? AppColors.primary : AppColors.border,
        ),
      ),
    );
  }

  Widget _buildSelectedFilesInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: AppColors.primary, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${_selectedFiles.length} file(s) selected to add to ${widget.category.name}',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _selectedFiles.clear();
              });
            },
            child: Text(
              'Clear',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFileTable(List<DocumentModel> documents) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: AppColors.border, width: 1),
                    ),
                  ),
                  child: Table(
                    columnWidths: const {
                      0: FixedColumnWidth(40), // Checkbox
                      1: FlexColumnWidth(4), // Name
                      2: FlexColumnWidth(2), // Type
                      3: FlexColumnWidth(2), // Date
                    },
                    children: [
                      TableRow(
                        children: [
                          TableCell(
                            child: Checkbox(
                              value:
                                  _selectedFiles.length == documents.length &&
                                  documents.isNotEmpty,
                              onChanged: (value) {
                                setState(() {
                                  if (value == true) {
                                    _selectedFiles.addAll(
                                      documents.map((doc) => doc.id),
                                    );
                                  } else {
                                    _selectedFiles.clear();
                                  }
                                });
                              },
                              activeColor: AppColors.primary,
                            ),
                          ),
                          TableCell(
                            child: Text(
                              'File Name',
                              style: _getTableHeaderStyle(),
                            ),
                          ),
                          TableCell(
                            child: Text(
                              'Type',
                              style: _getTableHeaderStyle(),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          TableCell(
                            child: Text(
                              'Date',
                              style: _getTableHeaderStyle(),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // File List
                SizedBox(
                  height: 400, // Fixed height for scrollable area
                  child: Scrollbar(
                    thumbVisibility: true,
                    child: ListView.builder(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemCount: documents.length,
                      itemBuilder: (context, index) {
                        final document = documents[index];
                        return _buildDocumentRow(document);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildDocumentRow(DocumentModel document) {
    final isSelected = _selectedFiles.contains(document.id);

    return Container(
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.primary.withValues(alpha: 0.05)
            : Colors.transparent,
        border: const Border(
          bottom: BorderSide(color: AppColors.border, width: 0.5),
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            if (isSelected) {
              _selectedFiles.remove(document.id);
            } else {
              _selectedFiles.add(document.id);
            }
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Table(
            columnWidths: const {
              0: FixedColumnWidth(40), // Checkbox
              1: FlexColumnWidth(4), // Name
              2: FlexColumnWidth(2), // Type
              3: FlexColumnWidth(2), // Date
            },
            children: [
              TableRow(
                children: [
                  TableCell(
                    verticalAlignment: TableCellVerticalAlignment.middle,
                    child: Checkbox(
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            _selectedFiles.add(document.id);
                          } else {
                            _selectedFiles.remove(document.id);
                          }
                        });
                      },
                      activeColor: AppColors.primary,
                    ),
                  ),
                  TableCell(
                    verticalAlignment: TableCellVerticalAlignment.middle,
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: _getFileTypeColor(
                              document.fileType,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            _getFileTypeIcon(document.fileType),
                            color: _getFileTypeColor(document.fileType),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            document.fileName,
                            style: _getTableTextStyle(),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TableCell(
                    verticalAlignment: TableCellVerticalAlignment.middle,
                    child: Text(
                      _getFileTypeLabel(document.fileType),
                      style: _getTableTextStyle(),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  TableCell(
                    verticalAlignment: TableCellVerticalAlignment.middle,
                    child: Text(
                      _formatDate(document.uploadedAt),
                      style: _getTableTextStyle(),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No available files found',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'All files are already in categories or try adjusting your search',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _addSelectedFiles() async {
    if (_selectedFiles.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final documentProvider = Provider.of<DocumentProvider>(
        context,
        listen: false,
      );

      // Update each selected document's category
      for (String fileId in _selectedFiles) {
        await documentProvider.updateDocumentCategory(
          fileId,
          widget.category.id,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_selectedFiles.length} file(s) added to ${widget.category.name}',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: AppColors.success,
          ),
        );

        // Navigate back to category files screen
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to add files: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Helper methods for styling and formatting
  TextStyle _getTableHeaderStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: AppColors.textPrimary,
    );
  }

  TextStyle _getTableTextStyle() {
    return GoogleFonts.poppins(
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: AppColors.textPrimary,
    );
  }

  IconData _getFileTypeIcon(String fileType) {
    if (fileType.contains('pdf')) return Icons.picture_as_pdf;
    if (fileType.contains('word') || fileType.contains('doc')) {
      return Icons.description;
    }
    if (fileType.contains('excel') || fileType.contains('sheet')) {
      return Icons.table_chart;
    }
    if (fileType.contains('powerpoint') || fileType.contains('presentation')) {
      return Icons.slideshow;
    }
    if (fileType.contains('image')) return Icons.image;
    if (fileType.contains('video')) return Icons.video_file;
    if (fileType.contains('audio')) return Icons.audio_file;
    return Icons.insert_drive_file;
  }

  Color _getFileTypeColor(String fileType) {
    if (fileType.contains('pdf')) return Colors.red;
    if (fileType.contains('word') || fileType.contains('doc')) {
      return Colors.blue;
    }
    if (fileType.contains('excel') || fileType.contains('sheet')) {
      return Colors.green;
    }
    if (fileType.contains('powerpoint') || fileType.contains('presentation')) {
      return Colors.orange;
    }
    if (fileType.contains('image')) return Colors.purple;
    if (fileType.contains('video')) return Colors.pink;
    if (fileType.contains('audio')) return Colors.teal;
    return AppColors.textSecondary;
  }

  String _getFileTypeLabel(String fileType) {
    if (fileType.contains('pdf')) return 'PDF';
    if (fileType.contains('word') || fileType.contains('doc')) return 'Word';
    if (fileType.contains('excel') || fileType.contains('sheet')) {
      return 'Excel';
    }
    if (fileType.contains('powerpoint') || fileType.contains('presentation')) {
      return 'PowerPoint';
    }
    if (fileType.contains('image')) return 'Image';
    if (fileType.contains('video')) return 'Video';
    if (fileType.contains('audio')) return 'Audio';
    return 'File';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
